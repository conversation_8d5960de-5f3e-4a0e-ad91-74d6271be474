import { useQuery } from "@tanstack/react-query";
import { z } from "zod";
import { GetServicesListService } from "../../../services/services/get-services";

// Provider Related Schemas
export const ProviderContactSchema = z.object({
  phone_number: z.string().optional(),
  address_email: z.string().optional(),
  phone_number_country_code: z.string().optional(),
});

export const ProviderAddressSchema = z.object({
  city: z.string().optional(),
  country: z.string().optional(),
});

export const ProviderUnsafeMetadataSchema = z.object({
  title: z.string().optional(),
  about_me: z.string().optional(),
  currency: z.string().optional(),
  language: z.string().optional(),
  username: z.string().optional(),
  phone_number: z.string().optional(),
  address_email: z.string().optional(),
  date_of_birth: z.string().optional(),
  notifications: z.array(z.string()).optional(),
  postal_address: ProviderAddressSchema.optional(),
  contact_address: ProviderContactSchema.optional(),
});

export const ProviderSchema = z.object({
  id: z.number(),
  extl_id: z.string(),
  note: z.string().nullable(),
  status: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  imageUrl: z.string().nullable(),
  email: z.string().nullable(),
  unsafe_metadata: ProviderUnsafeMetadataSchema.nullable().optional(),
});

// Support Related Schemas
export const SupportSchema = z.object({
  id: z.number(),
  name: z.string(),
  subject: z.string().optional(),
  message: z.string(),
  user: ProviderSchema.nullable(),
  status: z.string().optional(),
  priority: z.string().optional(),
  createdAt: z.string().nullable().optional(),
  updatedAt: z.string().nullable().optional(),
});

const servicesListSchema = z.array(SupportSchema);

export default function ServicePage() {
  const getServicesListService = new GetServicesListService();

  const { data } = useQuery({
    queryKey: ["services-list", { limit: 50, next: 1 }],
    queryFn: () =>
      getServicesListService
        .execute()
        .then((data) => servicesListSchema.parse(data)),
  });

  return (
    <div>
      {data?.map((service) => <div key={service.id}>{service.name}</div>)}
    </div>
  );
}
