import { BaseApiService } from "../base-service";

export class GetServicesListService extends BaseApiService {
  private BASE_SERVICES_URL = "services/";

  constructor() {
    super();
  }

  async execute(filters?: ServiceFilters) {
    const { limit = 50, next = 1, extl_id } = filters || {};
    let endpoint = `${this.BASE_SERVICES_URL}all/${limit}/${next}`;

    if (extl_id) {
      endpoint += `?extl_id=${extl_id}`;
    }
    return await this.request(endpoint, {
      method: "GET",
      tag: `services-list-${limit}-${next}-${extl_id || ""}`,
    });
  }
}
